// 导航组件加载器
(function() {
    // 加载导航组件
    function loadNavigation() {
        fetch('../component/navigation/navigation.html')
            .then(response => response.text())
            .then(html => {
                const navigationContainer = document.getElementById('navigation-container');
                if (navigationContainer) {
                    navigationContainer.innerHTML = html;
                }
            })
            .catch(error => {
                console.error('导航组件加载失败:', error);
            });
    }

    // 页面加载完成后执行
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', loadNavigation);
    } else {
        loadNavigation();
    }
})();
