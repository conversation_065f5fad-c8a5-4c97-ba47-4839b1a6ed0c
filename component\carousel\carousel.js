// 轮播图组件加载器和功能实现 (使用jQuery)
(function() {
    let currentSlide = 0;
    let slideInterval;
    let $slides, $indicators, $carouselContainer;

    // 加载轮播图组件
    function loadCarousel() {
        $.get('../component/carousel/carousel.html')
            .done(function(html) {
                $('#carousel-container').html(html);
                initCarousel();
            })
            .fail(function() {
                console.error('轮播图组件加载失败');
            });
    }

    // 初始化轮播图功能
    function initCarousel() {
        $slides = $('.carousel-slide');
        $indicators = $('.indicator');
        $carouselContainer = $('.carousel-container');

        if ($slides.length === 0) return;

        // 设置背景图片
        $slides.each(function() {
            const bgImage = $(this).data('bg');
            if (bgImage) {
                $(this).css('background-image', `url(${bgImage})`);
            }
        });

        // 绑定指示点点击事件
        $indicators.on('click', function() {
            const index = $(this).data('slide');
            goToSlide(index);
        });

        // 开始自动轮播
        startAutoSlide();

        // 鼠标悬停时暂停轮播
        $carouselContainer.on('mouseenter', stopAutoSlide);
        $carouselContainer.on('mouseleave', startAutoSlide);
    }

    // 切换到指定幻灯片
    function goToSlide(index) {
        if (index < 0 || index >= $slides.length) return;

        // 移除当前活动状态
        $slides.eq(currentSlide).removeClass('active');
        $indicators.eq(currentSlide).removeClass('active');

        // 设置新的活动状态
        currentSlide = index;
        $slides.eq(currentSlide).addClass('active');
        $indicators.eq(currentSlide).addClass('active');
    }

    // 下一张幻灯片
    function nextSlide() {
        const nextIndex = (currentSlide + 1) % $slides.length;
        goToSlide(nextIndex);
    }

    // 开始自动轮播
    function startAutoSlide() {
        stopAutoSlide(); // 先清除之前的定时器
        slideInterval = setInterval(nextSlide, 4000); // 每4秒切换一次
    }

    // 停止自动轮播
    function stopAutoSlide() {
        if (slideInterval) {
            clearInterval(slideInterval);
            slideInterval = null;
        }
    }

    // 页面加载完成后执行
    $(document).ready(function() {
        loadCarousel();
    });

    // 页面隐藏时停止轮播，显示时恢复
    $(document).on('visibilitychange', function() {
        if (document.hidden) {
            stopAutoSlide();
        } else {
            startAutoSlide();
        }
    });
})();
