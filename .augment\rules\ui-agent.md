---
type: "manual"
description: "globs:"
---
# HTML静态页面构建规则

## 使用场景
- 根据用户提供的UI图、HTML、CSS在指定页面添加静态UI界面
- 开发新的HTML页面或组件的布局和样式
- 需要将外部UI资源转换为项目内部统一规范的场景

## 关键规则
- **严守视觉还原**：严格按照UI图或参考代码进行视觉还原，但应以实现布局意图为核心。
- **禁止机械复刻**：**严禁**机械式地复制UI设计工具（如蓝湖）生成的DOM结构和布局代码（如 `margin`, `position`）。
- **理解并重构**：必须分析并理解设计的DOM结构和布局意图（如居中、两端对齐等），并使用语义化标签和 `Flexbox` 等现代CSS技术进行重构。
- **语义化命名**：在理解和重构布局后，必须对CSS类名进行有意义的命名，以反映其功能或内容，避免使用如 `box_1`、`group_2` 等无意义名称。
- **统一单位规范**：使用标准的CSS单位（px、em、rem、%等），根据具体需求选择合适的单位。
- **代码结构清晰**：采用语义化HTML标签，样式使用CSS或SCSS，保持代码结构清晰易维护。

## 详细指南

### 语义化HTML标签使用规则
- **内容区域**：使用 `<main>`、`<section>`、`<article>` 等语义化标签组织内容
- **图片处理**：优先使用 `<img>` 标签而非CSS背景图，确保可访问性和SEO友好

### 单位使用规则
- 使用px作为基础像素单位
- 使用rem或em用于字体大小，实现更好的响应式效果
- 使用%或vw/vh用于响应式布局
- 根据设计需求选择最合适的单位，无需强制转换

### 代码组织结构
- HTML结构使用标准的HTML5语义化标签
- CSS样式可以内联、内部样式表或外部样式表的形式组织
- 推荐使用外部CSS文件，便于维护和复用
- **必须包含响应式设计**：添加媒体查询以适配不同屏幕尺寸

### 布局实现原则
- **意图驱动开发**：在编码前，必须分析UI元素间的对齐与分布关系。UI工具导出的代码仅作为视觉与尺寸参考，其布局实现方式（通常是 `margin` 或 `position`）必须被更优的方式替代。
- **Flexbox优先**：对于一维布局（元素沿水平或垂直方向排列），**必须**优先使用Flexbox。通过 `justify-content`, `align-items`, 和 `gap` 属性控制主轴和交叉轴的对齐与间距。
- **Grid布局**：对于二维布局（行列网格），推荐使用CSS Grid布局，提供更强大的布局控制能力。
- **合理使用Margin**：`margin` 属性应主要用于元素与元素之间的微调间距，**严禁**使用大数值的 `margin` 来实现页面宏观布局定位。
- **复杂布局**：对于复杂的界面，应通过嵌套的Flexbox/Grid容器来构建，以保持布局的清晰和可扩展性。

### 响应式设计要求
- **包含媒体查询**：至少包含3个断点（1200px、900px、600px）
- **Grid响应式**：使用 `grid-template-columns: repeat(auto-fit, minmax(250px, 1fr))` 等自适应网格
- **图片响应式**：使用 `object-fit: cover` 和 `max-width: 100%` 确保图片适配
- **字体响应式**：在小屏幕上适当缩小字体大小

### 视觉还原原则
- 保持边距、内边距、字体大小、行高等与UI图一致
- 保持颜色、阴影、圆角等视觉效果与UI图一致
- 响应式布局应根据原始设计适配，使用媒体查询等技术
- **交互状态**：添加 `:hover`、`:focus`、`:active` 等状态样式

### 浏览器兼容性
- 确保代码在主流浏览器中正常显示
- 使用CSS前缀处理兼容性问题
- 考虑移动端和桌面端的适配

## 最佳实践示例

### 示例1：现代化导航结构
<example>
<!-- 正确的导航结构实现 -->
<header class="header">
  <div class="logo-section">
    <div class="logo-icon"></div>
    <div class="company-name">公司名称</div>
  </div>
  <nav class="main-nav">
    <a href="#" class="nav-link">关于我们</a>
    <a href="#" class="nav-link">产品服务</a>
    <a href="#" class="nav-link">新闻动态</a>
  </nav>
</header>

<section class="secondary-nav">
  <nav class="product-nav">
    <a href="#" class="product-link">产品列表</a>
    <a href="#" class="product-link">解决方案</a>
    <button class="contact-btn">联系我们</button>
  </nav>
</section>

<style>
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 0;
  max-width: 1200px;
  margin: 0 auto;
}

.logo-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.company-name {
  font-size: 24px;
  font-weight: 600;
  margin: 0;
}

.main-nav {
  display: flex;
  gap: 32px;
}

.nav-link {
  text-decoration: none;
  color: #333;
  transition: color 0.3s ease;
}

.nav-link:hover {
  color: #0066cc;
}
</style>
</example>

### 示例2：响应式网格布局
<example>
<!-- 正确的网格布局实现 -->
<section class="news-section">
  <header class="section-header">
    <div class="section-title">最新新闻</div>
  </header>
  <div class="news-grid">
    <article class="news-card">
      <img class="news-image" src="news1.jpg" alt="新闻标题">
      <div class="news-content">
        <div class="news-title">新闻标题</div>
        <p class="news-summary">新闻摘要内容...</p>
      </div>
    </article>
    <!-- 更多新闻卡片 -->
  </div>
</section>

<style>
.news-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
  margin-top: 32px;
}

.news-card {
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.news-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.news-image {
  width: 100%;
  height: 200px;
  object-fit: cover;
}

.news-content {
  padding: 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .news-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .news-card {
    margin: 0 16px;
  }
}
</style>
</example>

<example type="invalid">
<!-- 错误的HTML页面布局实现 -->
<!DOCTYPE html>
<html>
<head>
  <title>错误示例</title>
</head>
<body>
  <div class="c1">
    <div class="c2">
      <img class="c3" src="/static/image/product.png">
    </div>
    <div class="c4">
      <h3 class="c5">产品名称</h3>
      <div class="c6">¥99.00</div>
      <div class="c7">
        <button class="c8">立即购买</button>
      </div>
    </div>
  </div>
</body>
</html>

<style>
.c1 {
  width: 320px;
  padding: 16px;
  /* 错误：使用无意义的类名 */
}
.c2 {
  height: 200px;
  /* 错误：缺少语义化命名 */
}
.c3 {
  width: 100%;
  /* 错误：没有考虑图片适配 */
}
.c5 {
  font-size: 1rem;
  /* 错误：缺少合适的字体样式 */
}
.c6 {
  font-size: 18px;
  /* 错误：缺少颜色和字重设置 */
}
.c8 {
  width: 100%;
  height: 40px;
  background-color: #ff6700;
  color: #fff;
  /* 错误：缺少交互状态和过渡效果 */
}
</style>
</example>

## 常见错误与避免方法

### 错误示例1：不当的DOM结构
<example type="invalid">
<!-- 错误：使用背景图而非img标签 -->
<div class="news-image-1"></div>  <!-- 错误：不利于SEO和可访问性 -->

<style>
.news-image-1 {
  background-image: url(news.jpg);  /* 错误：应使用img标签 */
  width: 200px;
  height: 150px;
}
</style>
</example>

/* 错误：缺少响应式设计 */
.container {
  width: 1200px;  /* 错误：固定宽度，不适配移动端 */
}
</style>
</example>

## 强制检查清单

在完成HTML/CSS代码后，必须检查以下项目：

### HTML结构检查
- [ ] 导航使用了 `<nav>` 标签
- [ ] 内容区域使用了语义化标签（`<main>`, `<section>`, `<article>`）
- [ ] 图片使用了 `<img>` 标签并包含 `alt` 属性

### CSS布局检查
- [ ] 网格布局使用了 CSS Grid（`display: grid`）
- [ ] 一维布局使用了 Flexbox（`display: flex`）
- [ ] 包含了至少3个媒体查询断点
- [ ] 没有使用大数值的 `margin` 进行布局定位
- [ ] 添加了交互状态样式（`:hover`, `:focus`）

### 响应式检查
- [ ] 在1200px以下屏幕正常显示
- [ ] 在768px以下屏幕正常显示
- [ ] 在480px以下屏幕正常显示
- [ ] 图片在各种屏幕尺寸下正确缩放

### 性能检查
- [ ] 图片使用了 `object-fit` 属性
- [ ] 添加了过渡动画效果
- [ ] 避免了不必要的重复样式

## 注意事项
- **强制要求**：必须包含响应式设计，不得省略媒体查询
- **现代布局技术**：强制使用Grid进行二维布局，Flexbox进行一维布局
- **性能考虑**：图片必须使用img标签，确保SEO友好和可访问性
- **交互体验**：必须添加hover等交互状态，提升用户体验
- **代码质量**：保持代码结构清晰，便于后续维护和扩展

