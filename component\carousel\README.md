# 轮播图组件

基于jQuery的响应式轮播图组件，支持指示点导航和自动播放功能。

## 功能特性

- ✅ **自动轮播**: 每4秒自动切换，鼠标悬停暂停
- ✅ **指示点导航**: 底部圆点指示器，支持点击跳转
- ✅ **平滑过渡**: CSS3淡入淡出动画效果
- ✅ **响应式设计**: 适配不同屏幕尺寸
- ✅ **页面可见性检测**: 页面隐藏时自动暂停
- ✅ **无左右箭头**: 简洁的指示点设计

## 文件结构

```
component/carousel/
├── carousel.html    # 轮播图HTML模板
├── carousel.css     # 轮播图样式文件
├── carousel.js      # 轮播图JavaScript功能
├── demo.html        # 演示页面
├── test.html        # 测试页面
└── README.md        # 说明文档
```

## 使用方法

### 1. 引入依赖

确保页面已引入jQuery库：

```html
<script src="../assets/jquery-3.7.1.min.js"></script>
```

### 2. 引入组件文件

```html
<!-- CSS样式 -->
<link rel="stylesheet" type="text/css" href="../component/carousel/carousel.css">

<!-- JavaScript功能 -->
<script src="../component/carousel/carousel.js"></script>
```

### 3. 添加容器

在HTML中添加轮播图容器：

```html
<div id="carousel-container"></div>
```

### 4. 自动加载

组件会自动加载并初始化轮播图功能。

## 自定义配置

### 修改轮播间隔

在 `carousel.js` 中修改自动轮播间隔：

```javascript
slideInterval = setInterval(nextSlide, 4000); // 4秒间隔
```

### 添加更多幻灯片

在 `carousel.html` 中添加新的幻灯片：

```html
<div class="carousel-slide" data-bg="图片路径">
    <div class="slide-content">
        <h2 class="hero-title">标题文本</h2>
        <button class="cta-button">按钮文本</button>
    </div>
</div>
```

同时在指示器中添加对应的指示点：

```html
<span class="indicator" data-slide="3"></span>
```

### 样式自定义

主要CSS类说明：

- `.carousel-container`: 轮播图容器
- `.carousel-slide`: 单个幻灯片
- `.slide-content`: 幻灯片内容区域
- `.hero-title`: 标题样式
- `.cta-button`: 按钮样式
- `.carousel-indicators`: 指示点容器
- `.indicator`: 单个指示点
- `.indicator.active`: 激活状态的指示点

## 响应式断点

- **桌面端**: 1440px及以上
- **平板端**: 768px - 1439px
- **移动端**: 767px及以下

## 浏览器兼容性

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+
- IE 11+ (需要polyfill)

## 演示

打开 `demo.html` 查看完整的轮播图演示效果。

## 技术实现

- **jQuery**: 简化DOM操作和事件处理
- **CSS3 Transitions**: 提供平滑的过渡动画
- **Flexbox**: 响应式布局
- **CSS Grid**: 演示页面布局
- **Backdrop Filter**: 指示点背景模糊效果

## 注意事项

1. 确保图片路径正确，建议使用相对路径
2. 组件依赖jQuery，请确保jQuery在组件脚本之前加载
3. 如需修改轮播图尺寸，请同时调整CSS中的宽高设置
4. 建议图片尺寸保持一致，避免布局跳动

## 更新日志

### v1.0.0
- 初始版本发布
- 支持自动轮播和指示点导航
- 响应式设计
- jQuery集成
