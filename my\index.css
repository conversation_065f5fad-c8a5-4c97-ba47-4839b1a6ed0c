/* 基础样式重置和全局设置 */
.page {
  min-height: 100vh;
  background-color: #ffffff;
}

/* Hero Section - 头部区域 */
.hero-section {
  background-color: #000000;
  min-height: 520px;
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.hero-background {
  background: url(./img/SketchPngc22cd3bfbdf2b4c82b16fe88718024391d5b03af75c1b89a6da888553145132a.png) center center no-repeat;
  background-size: cover;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 40px;
}

.hero-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
  margin-top: 200px;
}

.company-logo-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.company-logo {
  width: 42px;
  height: 42px;
  border-radius: 50%;
  border: 1px solid #979797;
  overflow: hidden;
}

.logo-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.company-name {
  color: #ffffff;
  font-size: 30px;
  font-family: AlibabaPuHuiTi-Medium, sans-serif;
  font-weight: 500;
  margin: 0;
  line-height: 42px;
}

.hero-title-section {
  text-align: center;
}

.hero-title {
  color: #ffffff;
  font-size: 48px;
  font-family: SourceHanSansCN-Regular, sans-serif;
  margin: 0;
  line-height: 1.5;
}

.hero-subtitle-section {
  text-align: center;
}

.hero-subtitle {
  color: #ffffff;
  font-size: 24px;
  font-family: AlibabaPuHuiTi-Light, sans-serif;
  font-weight: 300;
  margin: 0;
  line-height: 1.4;
}

/* 次级导航 */
.secondary-nav {
  background-color: #090909;
  display: flex;
  align-items: center;
  gap: 64px;
  padding: 32px 50px;
  justify-content: flex-start;
}

.secondary-nav .nav-link {
  color: #ffffff;
  font-size: 18px;
  font-family: AlibabaPuHuiTi-Regular, sans-serif;
  text-decoration: none;
  line-height: 25px;
  transition: color 0.3s ease;
}

.secondary-nav .nav-link:hover {
  color: #0066cc;
}

.contact-btn {
  background-color: #ffffff;
  color: #000000;
  font-size: 18px;
  font-family: AlibabaPuHuiTi-Regular, sans-serif;
  padding: 31px 26px;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-left: auto;
}

.contact-btn:hover {
  background-color: #f0f0f0;
  transform: translateY(-2px);
}

/* 主导航 */
.main-nav {
  background-color: #ffffff;
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 34px 108px;
  position: absolute;
  top: 0;
  right: 0;
  height: 92px;
}

.hover-indicator {
  color: #ff1d1d;
  font-size: 14px;
  font-family: AlibabaPuHuiTi-Regular, sans-serif;
  line-height: 20px;
}

.nav-divider {
  width: 62px;
  height: 1px;
  margin: 0 8px;
}

.main-nav .nav-link {
  color: #000000;
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular, sans-serif;
  text-decoration: none;
  line-height: 22px;
  padding: 10px 20px;
  transition: all 0.3s ease;
}

.main-nav .nav-link:hover {
  color: #0e6be4;
  background-color: #f0f8ff;
}

.main-nav .nav-link.active {
  background-color: #0e6be4;
  color: #ffffff;
}

.nav-arrow {
  width: 14px;
  height: 14px;
  margin-left: auto;
}

/* 主要内容区域 */
.main-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 48px 20px;
}

/* 公司介绍部分 */
.company-intro {
  text-align: center;
  margin-bottom: 80px;
}

.intro-text {
  max-width: 871px;
  margin: 0 auto 43px;
  color: #000000;
  font-size: 24px;
  font-family: AlibabaPuHuiTi-Light, sans-serif;
  font-weight: 300;
  line-height: 1.4;
}

.section-title {
  color: #000000;
  font-size: 36px;
  font-family: AlibabaPuHuiTi-Regular, sans-serif;
  margin: 0 0 20px;
  line-height: 1.4;
}

.business-title {
  margin-top: 60px;
}

.business-description {
  max-width: 540px;
  margin: 11px auto 32px;
  color: #000000;
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular, sans-serif;
  line-height: 1.4;
}

/* 业务卡片网格 */
.business-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 24px;
  margin-top: 32px;
}

.business-card {
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(86, 86, 86, 0.15);
  overflow: hidden;
  transition: all 0.3s ease;
}

.business-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 8px 24px rgba(86, 86, 86, 0.25);
}

.card-content {
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 18px;
}

.card-image {
  width: 100%;
  height: 180px;
  object-fit: cover;
  border-radius: 4px;
}

.card-text {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.card-title {
  color: #000000;
  font-size: 18px;
  font-family: AlibabaPuHuiTi-Regular, sans-serif;
  margin: 0;
  line-height: 1.4;
}

.card-description {
  color: #000000;
  font-size: 14px;
  font-family: AlibabaPuHuiTi-Regular, sans-serif;
  line-height: 1.4;
  margin: 0;
}

/* 视频部分 */
.video-section {
  text-align: center;
  margin: 80px 0;
}

.video-trigger {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 20px;
  margin-bottom: 13px;
}

.video-text {
  color: #000000;
  font-size: 14px;
  font-family: AlibabaPuHuiTi-Regular, sans-serif;
  line-height: 20px;
}

.play-icon {
  width: 7px;
  height: 14px;
}

.video-container {
  border-radius: 6px;
  background: url(./img/90b2806365c44ec4ae6a3708ca84c279_mergeImage.png) center center no-repeat;
  background-size: cover;
  height: 500px;
  max-width: 860px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: transform 0.3s ease;
}

.video-container:hover {
  transform: scale(1.02);
}

.video-play-btn {
  width: 100px;
  height: 100px;
  transition: transform 0.3s ease;
}

.video-container:hover .video-play-btn {
  transform: scale(1.1);
}

/* 页脚部分 */
.footer-section {
  background: url(./img/SketchPngc665d90b5754568cc23004f00a8304b254b404ddd99665b5d13240330c29f054.png) center center no-repeat;
  background-size: cover;
  margin: 100px 0 0;
  padding: 40px 20px;
}

.footer-content {
  max-width: 1030px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.footer-links {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 40px;
  padding: 18px 60px 0;
}

.footer-column {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.footer-title {
  color: #ffffff;
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Bold, sans-serif;
  font-weight: 700;
  margin: 0;
  line-height: 22px;
}

.footer-link {
  color: #ffffff;
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular, sans-serif;
  text-decoration: none;
  line-height: 22px;
  transition: color 0.3s ease;
}

.footer-link:hover {
  color: #cccccc;
}

.footer-brand {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 0 20px;
}

.footer-logo {
  width: 56px;
  height: 55px;
}

.footer-company-name {
  color: #000000;
  font-size: 30px;
  font-family: AlibabaPuHuiTi-Medium, sans-serif;
  font-weight: 500;
  line-height: 42px;
}

/* 公司使命部分 */
.company-mission {
  background: url(./img/b8d447e677534c7b957e2e9fc11992af_mergeImage.png) center center no-repeat;
  background-size: cover;
  border-radius: 8px;
  padding: 47px 40px;
  margin: 80px 0;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.mission-content {
  max-width: 540px;
  margin-left: auto;
}

.mission-title {
  color: #000000;
  font-size: 36px;
  font-family: AlibabaPuHuiTi-Regular, sans-serif;
  margin: 0 0 21px;
  line-height: 1.4;
  text-align: right;
}

.mission-text {
  color: #000000;
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular, sans-serif;
  line-height: 1.4;
  text-align: justify;
  margin: 0;
}

/* 战略部分 */
.strategy-section {
  margin: 80px 0;
}

.strategy-title {
  color: #000000;
  font-size: 36px;
  font-family: AlibabaPuHuiTi-Regular, sans-serif;
  margin: 0 0 23px;
  line-height: 1.4;
  text-align: right;
}

.strategy-content {
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  padding: 37px 40px;
  display: flex;
  gap: 37px;
  align-items: flex-start;
}

.strategy-text {
  flex: 1;
  max-width: 540px;
}

.strategy-subtitle {
  color: #000000;
  font-size: 36px;
  font-family: AlibabaPuHuiTi-Regular, sans-serif;
  margin: 0 0 21px;
  line-height: 1.4;
  text-align: right;
}

.strategy-description {
  color: #000000;
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular, sans-serif;
  line-height: 1.4;
  text-align: justify;
  margin: 0;
}

.strategy-image {
  width: 360px;
  height: 360px;
  object-fit: cover;
  border-radius: 8px;
}

/* 多元化部分 */
.diversity-section {
  margin: 80px 0;
}

.diversity-title {
  color: #000000;
  font-size: 36px;
  font-family: AlibabaPuHuiTi-Medium, sans-serif;
  font-weight: 500;
  margin: 0 0 23px;
  line-height: 1.4;
  text-align: right;
}

.diversity-content {
  background-color: #ffffff;
  border-radius: 8px;
  padding: 27px 40px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.diversity-text {
  color: #000000;
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular, sans-serif;
  line-height: 1.4;
  text-align: justify;
  margin: 0;
  max-width: 840px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .main-content {
    max-width: 960px;
    padding: 40px 20px;
  }

  .hero-background {
    padding: 30px 20px;
  }

  .hero-content {
    margin-top: 150px;
  }

  .business-cards {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
  }

  .strategy-content {
    flex-direction: column;
    gap: 30px;
  }

  .strategy-image {
    width: 100%;
    max-width: 360px;
    height: auto;
    align-self: center;
  }

  .video-container {
    max-width: 700px;
    height: 400px;
  }

  .footer-section {
    padding: 30px 20px;
  }
}

@media (max-width: 900px) {
  .hero-section {
    min-height: 400px;
  }

  .hero-content {
    margin-top: 100px;
    gap: 15px;
  }

  .company-name {
    font-size: 24px;
  }

  .hero-title {
    font-size: 36px;
  }

  .hero-subtitle {
    font-size: 20px;
  }

  .secondary-nav {
    flex-wrap: wrap;
    gap: 20px;
    padding: 20px;
  }

  .main-nav {
    position: static;
    flex-wrap: wrap;
    padding: 20px;
    gap: 15px;
  }

  .main-content {
    padding: 30px 15px;
  }

  .intro-text {
    font-size: 20px;
  }

  .section-title {
    font-size: 28px;
  }

  .business-cards {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .video-container {
    height: 300px;
  }

  .video-play-btn {
    width: 80px;
    height: 80px;
  }

  .mission-title,
  .strategy-title,
  .strategy-subtitle,
  .diversity-title {
    font-size: 28px;
    text-align: center;
  }

  .footer-links {
    grid-template-columns: 1fr;
    gap: 20px;
    padding: 15px 20px 0;
  }
}

@media (max-width: 600px) {
  .hero-background {
    padding: 20px 15px;
  }

  .hero-content {
    margin-top: 80px;
    gap: 12px;
  }

  .company-logo {
    width: 36px;
    height: 36px;
  }

  .company-name {
    font-size: 20px;
    line-height: 36px;
  }

  .hero-title {
    font-size: 28px;
  }

  .hero-subtitle {
    font-size: 18px;
  }

  .secondary-nav {
    flex-direction: column;
    align-items: stretch;
    gap: 15px;
    padding: 15px;
  }

  .contact-btn {
    margin-left: 0;
    padding: 15px 20px;
  }

  .main-nav {
    flex-direction: column;
    align-items: stretch;
    padding: 15px;
    gap: 10px;
  }

  .main-content {
    padding: 20px 10px;
  }

  .intro-text {
    font-size: 18px;
  }

  .section-title {
    font-size: 24px;
  }

  .business-description {
    font-size: 14px;
  }

  .card-content {
    padding: 15px;
  }

  .card-image {
    height: 150px;
  }

  .video-container {
    height: 250px;
  }

  .video-play-btn {
    width: 60px;
    height: 60px;
  }

  .mission-content,
  .strategy-text,
  .diversity-content {
    padding: 20px;
  }

  .mission-title,
  .strategy-title,
  .strategy-subtitle,
  .diversity-title {
    font-size: 22px;
  }

  .mission-text,
  .strategy-description,
  .diversity-text {
    font-size: 14px;
  }

  .footer-brand {
    flex-direction: column;
    text-align: center;
    gap: 8px;
  }

  .footer-company-name {
    font-size: 24px;
  }
}
